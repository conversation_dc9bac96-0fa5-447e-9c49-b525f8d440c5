{"name": "tv-recs-openai", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "svelte-kit sync && vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write ."}, "devDependencies": {"@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "autoprefixer": "^10.4.13", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "postcss": "^8.4.21", "prettier": "^2.8.0", "prettier-plugin-svelte": "^2.10.1", "svelte": "^4.0.0", "svelte-check": "^3.4.3", "tailwindcss": "^3.2.4", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite": "^5.2.10"}, "type": "module", "dependencies": {"@sveltejs/adapter-vercel": "^4.0.0", "@types/geoip-lite": "^1.4.4", "@vercel/analytics": "^1.5.0", "@vercel/kv": "^0.2.2", "@xenova/transformers": "^2.17.2", "esbuild": "^0.18.11", "eventsource-parser": "^0.1.0", "geoip-lite": "^1.4.10", "mongodb": "^6.17.0"}}