<script>
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { fade } from 'svelte/transition';
	import Home from '$lib/Home.svelte';
	import Footer from '$lib/Footer.svelte';
	import Header from '$lib/Header.svelte';

	let isLoggedIn = false;
	let userData = null;

	// Check for existing login on mount
	onMount(() => {
		const storedUser = localStorage.getItem('user');
		if (storedUser) {
			try {
				userData = JSON.parse(storedUser);
				isLoggedIn = true;
				// Redirect to recommendations page if already logged in
				goto('/recommendations');
			} catch (err) {
				localStorage.removeItem('user');
			}
		}
	});

	// Handle login success
	function handleLogin(event) {
		userData = event.detail;
		isLoggedIn = true;
		// Redirect to recommendations page after login
		goto('/recommendations');
	}

	// Handle logout
	function handleLogout() {
		localStorage.removeItem('user');
		userData = null;
		isLoggedIn = false;
	}


</script>

<svelte:head>
	<title>Login - Cinemated</title>
</svelte:head>

<div>
	<div class="h-screen w-full bg-cover fixed" style="background-image: url(/background.png)">
		<div class="flex flex-col items-center justify-center min-h-screen w-full h-full bg-gradient-to-br from-slate-900/80 to-black/90" />
	</div>

	<div class="absolute inset-0 px-6 flex flex-col h-screen overflor-auto">
		<Header
			{isLoggedIn}
			{userData}
			on:click={() => {
				// No action needed on home page
			}}
			on:logout={handleLogout}
		/>

		<div
			in:fade|global
			class="flex-grow max-w-4xl mx-auto w-full md:pt-20 flex flex-col items-center justify-center">
			<Home
				on:login={handleLogin}
			/>
		</div>

		<Footer />
	</div>
</div>
