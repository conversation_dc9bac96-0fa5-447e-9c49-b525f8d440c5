<script>
	import '../app.css';
	import { dev } from '$app/environment';

	// Only load analytics in production to avoid SvelteKit router conflicts during development
	if (!dev) {
		import('@vercel/analytics').then(({ inject }) => {
			inject({ mode: 'production' });
		}).catch(() => {
			// Silently fail if analytics can't be loaded
		});
	}
</script>

<div class="min-h-screen w-full">
	<slot />
</div>
