<script>
	import '../app.css';
	import { dev } from '$app/environment';
	import { onMount } from 'svelte';

	// Only load analytics in production
	onMount(async () => {
		if (!dev) {
			try {
				const { inject } = await import('@vercel/analytics');
				inject();
			} catch (error) {
				// Silently fail if analytics can't be loaded
				console.warn('Analytics failed to load:', error);
			}
		}
	});
</script>

<div class="min-h-screen w-full">
	<slot />
</div>
