<script>
	import { onMount } from 'svelte';
	import { createEventDispatcher } from 'svelte';
	import Modal from './Modal.svelte';
	import { generateSecretKey } from '$lib/utils/secretKey';

	/**
	 * @type {{ email: any; }}
	 */
	 export let userData;

	const dispatch = createEventDispatcher();

	/**
	 * @type {any[]}
	 */
	let users = [];
	/**
	 * @type {any[]}
	 */
	let registrationRequests = [];
	let loading = false;
	let error = '';
	let activeTab = 'users'; // 'users', 'pending_verification', 'rejected', or 'requests'

	// Modal state
	let showVerificationModal = false;
	let isResendMode = false; // Track if this is a resend or initial approval
	let verificationData = {
		email: '',
		secretKey: '',
		verificationCode: '',
		verificationLink: ''
	};

	// Copy feedback state
	let copyFeedback = '';
	/**
	 * @type {string | number | NodeJS.Timeout | null | undefined}
	 */
	let copyTimeout = null;

	onMount(() => {
		loadUsers();
		loadRegistrationRequests();
	});

	async function loadUsers() {
		loading = true;
		try {
			const response = await fetch('/api/admin/users', {
				headers: {
					'Authorization': `Admin ${userData.email}`
				}
			});

			if (response.ok) {
				const data = await response.json();
				users = data.users;
			} else {
				error = 'Failed to load users';
			}
		} catch (err) {
			error = 'Network error loading users';
		}
		loading = false;
	}

	async function loadRegistrationRequests() {
		try {
			const response = await fetch('/api/admin/registration-requests');
			if (response.ok) {
				const data = await response.json();
				registrationRequests = data.requests;
			}
		} catch (err) {
			console.error('Error loading registration requests:', err);
		}
	}

	/**
	 * @param {any} userId
	 * @param {boolean} currentStatus
	 */
	async function toggleUserStatus(userId, currentStatus) {
		try {
			const response = await fetch('/api/admin/users', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Admin ${userData.email}`
				},
				body: JSON.stringify({
					userId: userId,
					action: 'toggle_status',
					isEnabled: !currentStatus
				})
			});

			if (response.ok) {
				await loadUsers(); // Reload users
			} else {
				error = 'Failed to update user status';
			}
		} catch (err) {
			error = 'Network error updating user';
		}
	}

	/**
	 * @param {any} requestId
	 * @param {string} secretKey
	 */
	async function approveRequest(requestId, secretKey) {
		try {
			const response = await fetch('/api/admin/registration-requests', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					requestId: requestId,
					action: 'approve',
					secretKey: secretKey
				})
			});

			if (response.ok) {
				const data = await response.json();
				// Show verification data in modal
				verificationData = {
					email: data.user.email,
					secretKey: data.user.secretKey,
					verificationCode: data.user.verificationCode,
					verificationLink: `${window.location.origin}/email-verify?code=${data.user.verificationCode}`
				};
				isResendMode = false; // This is initial approval
				showVerificationModal = true;
				await loadRegistrationRequests();
				await loadUsers();
			} else {
				error = 'Failed to approve request';
			}
		} catch (err) {
			error = 'Network error approving request';
		}
	}



	/**
	 * @param {any} requestId
	 */
	async function rejectRequest(requestId) {
		try {
			const response = await fetch('/api/admin/registration-requests', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					requestId: requestId,
					action: 'reject'
				})
			});

			if (response.ok) {
				await loadRegistrationRequests();
			} else {
				error = 'Failed to reject request';
			}
		} catch (err) {
			error = 'Network error rejecting request';
		}
	}

	/**
	 * @param {any} userEmail
	 */
	async function regenerateVerificationCode(userEmail) {
		try {
			const response = await fetch('/api/admin/regenerate-verification', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Admin ${userData.email}`
				},
				body: JSON.stringify({
					email: userEmail
				})
			});

			if (response.ok) {
				const data = await response.json();
				// Show verification data in modal (same as approval)
				verificationData = {
					email: data.email,
					secretKey: data.secretKey,
					verificationCode: data.verificationCode,
					verificationLink: `${window.location.origin}/email-verify?code=${data.verificationCode}`
				};
				isResendMode = true; // This is a resend operation
				showVerificationModal = true;
			} else {
				error = 'Failed to regenerate verification code';
			}
		} catch (err) {
			error = 'Network error regenerating verification code';
		}
	}

	/**
	 * @param {any} requestId
	 */
	async function reconsiderRejection(requestId) {
		try {
			const response = await fetch('/api/admin/registration-requests', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					requestId: requestId,
					action: 'reconsider'
				})
			});

			if (response.ok) {
				await loadRegistrationRequests();
			} else {
				error = 'Failed to reconsider rejection';
			}
		} catch (err) {
			error = 'Network error reconsidering rejection';
		}
	}

	/**
	 * @param {any} requestId
	 */
	async function deleteRejectedRequest(requestId) {
		if (!confirm('Are you sure you want to permanently delete this rejected request? This action cannot be undone.')) {
			return;
		}

		try {
			const response = await fetch('/api/admin/registration-requests', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					requestId: requestId,
					action: 'delete'
				})
			});

			if (response.ok) {
				await loadRegistrationRequests();
			} else {
				error = 'Failed to delete rejected request';
			}
		} catch (err) {
			error = 'Network error deleting rejected request';
		}
	}

	function logout() {
		dispatch('logout');
	}

	/**
	 * @param {string | number | Date} dateString
	 */
	function formatDate(dateString) {
		return new Date(dateString).toLocaleString();
	}

	/**
	 * @param {string} text
	 */
	async function copyToClipboard(text, feedbackText = 'Copied!') {
		try {
			await navigator.clipboard.writeText(text);
			showCopyFeedback(feedbackText);
		} catch (err) {
			console.error('Failed to copy:', err);
			showCopyFeedback('Copy failed');
		}
	}

	/**
	 * @param {string} message
	 */
	function showCopyFeedback(message) {
		copyFeedback = message;
		if (copyTimeout) {
			clearTimeout(copyTimeout);
		}
		copyTimeout = setTimeout(() => {
			copyFeedback = '';
		}, 2000);
	}


</script>

<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4">
	<div class="max-w-7xl mx-auto">
		<!-- Header -->
		<div class="flex justify-between items-center mb-8">
			<h1 class="text-3xl font-bold text-white">Admin Panel</h1>
			<div class="flex items-center gap-4">
				<span class="text-white/70">Welcome, {userData.email}</span>
				<a
					href="/"
					class="bg-turquoise-600 hover:bg-turquoise-700 text-white px-4 py-2 rounded-md"
				>
					Back to App
				</a>
				<button
					on:click={logout}
					class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md"
				>
					Logout
				</button>
			</div>
		</div>

		{#if error}
			<div class="bg-red-500/20 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
				{error}
			</div>
		{/if}

		<!-- Tabs -->
		<div class="flex space-x-1 mb-6 flex-wrap">
			<button
				class="px-4 py-2 rounded-md {activeTab === 'users' ? 'bg-turquoise-600 text-white' : 'bg-white/10 text-white/70 hover:bg-white/20'}"
				on:click={() => activeTab = 'users'}
			>
				Verified Users ({users.filter(u => u.emailVerified === true).length})
			</button>
			<button
				class="px-4 py-2 rounded-md {activeTab === 'pending_verification' ? 'bg-turquoise-600 text-white' : 'bg-white/10 text-white/70 hover:bg-white/20'}"
				on:click={() => activeTab = 'pending_verification'}
			>
				Pending Email Verification ({users.filter(u => u.emailVerified === false).length})
			</button>
			<button
				class="px-4 py-2 rounded-md {activeTab === 'rejected' ? 'bg-turquoise-600 text-white' : 'bg-white/10 text-white/70 hover:bg-white/20'}"
				on:click={() => activeTab = 'rejected'}
			>
				Rejected Users ({registrationRequests.filter(r => r.status === 'rejected').length})
			</button>
			<button
				class="px-4 py-2 rounded-md {activeTab === 'requests' ? 'bg-turquoise-600 text-white' : 'bg-white/10 text-white/70 hover:bg-white/20'}"
				on:click={() => activeTab = 'requests'}
			>
				Registration Requests ({registrationRequests.filter(r => r.status === 'pending').length})
			</button>
		</div>

		{#if loading}
			<div class="text-center text-white py-8">Loading...</div>
		{:else if activeTab === 'users'}
			<!-- Verified Users Table -->
			<div class="mb-4 bg-green-500/10 border border-green-500/20 rounded-lg p-4">
				<div class="flex items-start">
					<svg class="w-5 h-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
					</svg>
					<div class="text-green-300 text-sm">
						<p class="font-medium mb-1">Verified Users</p>
						<p class="text-green-300/80">Users who have completed email verification and can fully access the system.</p>
					</div>
				</div>
			</div>

			<div class="bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden">
				<div class="overflow-x-auto">
					<table class="w-full">
						<thead class="bg-white/20">
							<tr>
								<th class="px-4 py-3 text-left text-white font-medium">Email</th>
								<th class="px-4 py-3 text-left text-white font-medium">Country</th>
								<th class="px-4 py-3 text-left text-white font-medium">Type</th>
								<th class="px-4 py-3 text-left text-white font-medium">Status</th>
								<th class="px-4 py-3 text-left text-white font-medium">Email Verified</th>
								<th class="px-4 py-3 text-left text-white font-medium">Registration IP</th>
								<th class="px-4 py-3 text-left text-white font-medium">Last Login</th>
								<th class="px-4 py-3 text-left text-white font-medium">Last Login IP</th>
								<th class="px-4 py-3 text-left text-white font-medium">Actions</th>
							</tr>
						</thead>
						<tbody>
							{#each users.filter(u => u.emailVerified === true) as user}
								<tr class="border-t border-white/10">
									<td class="px-4 py-3 text-white">{user.email}</td>
									<td class="px-4 py-3 text-white/70">{user.country_code || 'N/A'}</td>
									<td class="px-4 py-3">
										<span class="px-2 py-1 rounded text-xs {user.userType === 'admin' ? 'bg-purple-600 text-white' : 'bg-blue-600 text-white'}">
											{user.userType || 'user'}
										</span>
									</td>
									<td class="px-4 py-3">
										<span class="px-2 py-1 rounded text-xs {user.isEnabled !== false ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}">
											{user.isEnabled !== false ? 'Enabled' : 'Disabled'}
										</span>
									</td>
									<td class="px-4 py-3">
										<span class="px-2 py-1 rounded text-xs bg-green-600 text-white">
											Verified
										</span>
									</td>
									<td class="px-4 py-3 text-white/70 text-sm font-mono">{user.registrationIP || 'N/A'}</td>
									<td class="px-4 py-3 text-white/70 text-sm">
										{#if user.lastLoginAt}
											<div>{formatDate(user.lastLoginAt)}</div>
										{:else}
											<span class="text-white/50">Never</span>
										{/if}
									</td>
									<td class="px-4 py-3 text-white/70 text-sm font-mono">{user.lastLoginIP || 'N/A'}</td>
									<td class="px-4 py-3">
										{#if user.userType !== 'admin'}
											<button
												on:click={() => toggleUserStatus(user._id, user.isEnabled !== false)}
												class="px-3 py-1 rounded text-xs {user.isEnabled !== false ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'} text-white"
											>
												{user.isEnabled !== false ? 'Disable' : 'Enable'}
											</button>
										{:else}
											<span class="text-white/50 text-xs">Admin</span>
										{/if}
									</td>
								</tr>
							{:else}
								<tr>
									<td colspan="9" class="px-4 py-8 text-center text-white/50">
										No verified users yet
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			</div>
		{:else if activeTab === 'pending_verification'}
			<!-- Pending Email Verification Table -->
			<div class="mb-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
				<div class="flex items-start">
					<svg class="w-5 h-5 text-yellow-400 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
					</svg>
					<div class="text-yellow-300 text-sm">
						<p class="font-medium mb-1">Pending Email Verification</p>
						<p class="text-yellow-300/80">Users who have been approved but haven't verified their email yet. They cannot login until email verification is complete.</p>
					</div>
				</div>
			</div>

			<div class="bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden">
				<div class="overflow-x-auto">
					<table class="w-full">
						<thead class="bg-white/20">
							<tr>
								<th class="px-4 py-3 text-left text-white font-medium">Email</th>
								<th class="px-4 py-3 text-left text-white font-medium">Country</th>
								<th class="px-4 py-3 text-left text-white font-medium">Type</th>
								<th class="px-4 py-3 text-left text-white font-medium">Status</th>
								<th class="px-4 py-3 text-left text-white font-medium">Registration IP</th>
								<th class="px-4 py-3 text-left text-white font-medium">Created</th>
								<th class="px-4 py-3 text-left text-white font-medium">Actions</th>
							</tr>
						</thead>
						<tbody>
							{#each users.filter(u => u.emailVerified === false) as user}
								<tr class="border-t border-white/10">
									<td class="px-4 py-3 text-white">{user.email}</td>
									<td class="px-4 py-3 text-white/70">{user.country_code || 'N/A'}</td>
									<td class="px-4 py-3">
										<span class="px-2 py-1 rounded text-xs {user.userType === 'admin' ? 'bg-purple-600 text-white' : 'bg-blue-600 text-white'}">
											{user.userType || 'user'}
										</span>
									</td>
									<td class="px-4 py-3">
										<span class="px-2 py-1 rounded text-xs {user.isEnabled !== false ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}">
											{user.isEnabled !== false ? 'Enabled' : 'Disabled'}
										</span>
									</td>
									<td class="px-4 py-3 text-white/70 text-sm font-mono">{user.registrationIP || 'N/A'}</td>
									<td class="px-4 py-3 text-white/70 text-sm">{formatDate(user.createdAt)}</td>
									<td class="px-4 py-3">
										<div class="flex gap-2">
											<button
												on:click={() => regenerateVerificationCode(user.email)}
												class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs"
											>
												Resend Verification
											</button>
											{#if user.userType !== 'admin'}
												<button
													on:click={() => toggleUserStatus(user._id, user.isEnabled !== false)}
													class="px-3 py-1 rounded text-xs {user.isEnabled !== false ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'} text-white"
												>
													{user.isEnabled !== false ? 'Disable' : 'Enable'}
												</button>
											{/if}
										</div>
									</td>
								</tr>
							{:else}
								<tr>
									<td colspan="7" class="px-4 py-8 text-center text-white/50">
										No users pending email verification
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			</div>
		{:else if activeTab === 'rejected'}
			<!-- Rejected Users Table -->
			<div class="mb-4 bg-red-500/10 border border-red-500/20 rounded-lg p-4">
				<div class="flex items-start">
					<svg class="w-5 h-5 text-red-400 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
					<div class="text-red-300 text-sm">
						<p class="font-medium mb-1">Rejected Users</p>
						<p class="text-red-300/80">Registration requests that have been rejected by administrators. These users cannot access the system.</p>
					</div>
				</div>
			</div>

			<div class="bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden">
				<div class="overflow-x-auto">
					<table class="w-full">
						<thead class="bg-white/20">
							<tr>
								<th class="px-4 py-3 text-left text-white font-medium">Email</th>
								<th class="px-4 py-3 text-left text-white font-medium">Country</th>
								<th class="px-4 py-3 text-left text-white font-medium">Request IP</th>
								<th class="px-4 py-3 text-left text-white font-medium">Status</th>
								<th class="px-4 py-3 text-left text-white font-medium">Requested</th>
								<th class="px-4 py-3 text-left text-white font-medium">Rejected</th>
								<th class="px-4 py-3 text-left text-white font-medium">Actions</th>
							</tr>
						</thead>
						<tbody>
							{#each registrationRequests.filter(r => r.status === 'rejected') as request}
								<tr class="border-t border-white/10">
									<td class="px-4 py-3 text-white">{request.email}</td>
									<td class="px-4 py-3 text-white/70">{request.country_code}</td>
									<td class="px-4 py-3 text-white/70 text-sm font-mono">{request.requestIP || 'N/A'}</td>
									<td class="px-4 py-3">
										<span class="px-2 py-1 rounded text-xs bg-red-600 text-white">
											{request.status}
										</span>
									</td>
									<td class="px-4 py-3 text-white/70 text-sm">{formatDate(request.requestedAt)}</td>
									<td class="px-4 py-3 text-white/70 text-sm">
										{#if request.rejectedAt}
											{formatDate(request.rejectedAt)}
										{:else}
											<span class="text-white/50">N/A</span>
										{/if}
									</td>
									<td class="px-4 py-3">
										<div class="flex gap-2">
											<button
												on:click={() => reconsiderRejection(request._id)}
												class="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-xs"
											>
												Reconsider
											</button>
											<button
												on:click={() => deleteRejectedRequest(request._id)}
												class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs"
											>
												Delete
											</button>
										</div>
									</td>
								</tr>
							{:else}
								<tr>
									<td colspan="7" class="px-4 py-8 text-center text-white/50">
										No rejected users
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			</div>
		{:else}
			<!-- Registration Requests Table -->
			<div class="mb-4 bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
				<div class="flex items-start">
					<svg class="w-5 h-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
					<div class="text-blue-300 text-sm">
						<p class="font-medium mb-1">Registration Requests</p>
						<p class="text-blue-300/80">This section shows only pending requests that need your action. Once approved or rejected, users will appear in the Users tab above.</p>
					</div>
				</div>
			</div>

			<div class="bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden">
				<div class="overflow-x-auto">
					<table class="w-full">
						<thead class="bg-white/20">
							<tr>
								<th class="px-4 py-3 text-left text-white font-medium">Email</th>
								<th class="px-4 py-3 text-left text-white font-medium">Country</th>
								<th class="px-4 py-3 text-left text-white font-medium">Request IP</th>
								<th class="px-4 py-3 text-left text-white font-medium">Status</th>
								<th class="px-4 py-3 text-left text-white font-medium">Requested</th>
								<th class="px-4 py-3 text-left text-white font-medium">Actions</th>
							</tr>
						</thead>
						<tbody>
							{#each registrationRequests.filter(r => r.status === 'pending') as request}
								<tr class="border-t border-white/10">
									<td class="px-4 py-3 text-white">{request.email}</td>
									<td class="px-4 py-3 text-white/70">{request.country_code}</td>
									<td class="px-4 py-3 text-white/70 text-sm font-mono">{request.requestIP || 'N/A'}</td>
									<td class="px-4 py-3">
										<span class="px-2 py-1 rounded text-xs bg-yellow-600 text-white">
											{request.status}
										</span>
									</td>
									<td class="px-4 py-3 text-white/70 text-sm">{formatDate(request.requestedAt)}</td>
									<td class="px-4 py-3">
										<div class="flex gap-2">
											<button
												on:click={() => approveRequest(request._id, generateSecretKey())}
												class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-xs"
											>
												Approve
											</button>
											<button
												on:click={() => rejectRequest(request._id)}
												class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs"
											>
												Reject
											</button>
										</div>
									</td>
								</tr>
							{:else}
								<tr>
									<td colspan="6" class="px-4 py-8 text-center text-white/50">
										No pending registration requests
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			</div>
		{/if}
	</div>
</div>

<!-- Verification Modal -->
<Modal bind:isOpen={showVerificationModal} title="{isResendMode ? 'Verification Resent - Send Updated Email' : 'User Approved - Send Welcome Email'}" maxWidth="max-w-4xl">
	<!-- Copy Feedback -->
	{#if copyFeedback}
		<div class="fixed top-4 right-4 z-50 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg flex items-center">
			<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
			</svg>
			{copyFeedback}
		</div>
	{/if}

	<div class="space-y-6">
		<div class="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
			<div class="flex items-center mb-2">
				<svg class="w-5 h-5 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
				</svg>
				<span class="text-green-400 font-medium">
					{isResendMode ? 'Verification Resent Successfully!' : 'Registration Approved Successfully!'}
				</span>
			</div>
			<p class="text-green-300 text-sm">
				{isResendMode
					? 'New verification code and secret key have been generated. Please send the updated email to the user.'
					: 'User account has been created. Copy the email content below and send it to the user.'}
			</p>
		</div>

		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- User Information -->
			<div class="space-y-4">
				<h3 class="text-lg font-medium text-white mb-4">User Information</h3>

				<div>
					<label class="block text-sm font-medium text-white/70 mb-2">Send Email To:</label>
					<div class="bg-slate-700 rounded-md p-3 flex items-center justify-between">
						<code class="text-turquoise-400 font-mono text-sm">{verificationData.email}</code>
						<button
							on:click={() => copyToClipboard(verificationData.email, 'Email copied!')}
							class="ml-2 px-2 py-1 bg-slate-600 hover:bg-slate-500 text-white text-xs rounded"
						>
							Copy
						</button>
					</div>
				</div>

				{#if verificationData.secretKey}
					<div>
						<label class="block text-sm font-medium text-white/70 mb-2">Secret Key (for login)</label>
						<div class="bg-slate-700 rounded-md p-3 flex items-center justify-between">
							<code class="text-yellow-400 font-mono text-sm">{verificationData.secretKey}</code>
							<button
								on:click={() => copyToClipboard(verificationData.secretKey, 'Secret key copied!')}
								class="ml-2 px-2 py-1 bg-slate-600 hover:bg-slate-500 text-white text-xs rounded"
							>
								Copy
							</button>
						</div>
					</div>
				{/if}

				<div>
					<label class="block text-sm font-medium text-white/70 mb-2">Verification Link</label>
					<div class="bg-slate-700 rounded-md p-3">
						<div class="flex items-center justify-between">
							<code class="text-green-400 font-mono text-xs break-all mr-2">{verificationData.verificationLink}</code>
							<button
								on:click={() => copyToClipboard(verificationData.verificationLink, 'Link copied!')}
								class="ml-2 px-2 py-1 bg-slate-600 hover:bg-slate-500 text-white text-xs rounded flex-shrink-0"
							>
								Copy
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- Email Content -->
			<div class="space-y-4">
				<h3 class="text-lg font-medium text-white mb-4">Email Content to Send</h3>

				<div>
					<label class="block text-sm font-medium text-white/70 mb-2">Subject Line:</label>
					<div class="bg-slate-700 rounded-md p-3 flex items-center justify-between">
						<span class="text-white text-sm">Welcome to Cinemated - Verify Your Email</span>
						<button
							on:click={() => copyToClipboard('Welcome to Cinemated - Verify Your Email', 'Subject copied!')}
							class="ml-2 px-2 py-1 bg-slate-600 hover:bg-slate-500 text-white text-xs rounded"
						>
							Copy
						</button>
					</div>
				</div>

				<div>
					<label class="block text-sm font-medium text-white/70 mb-2">Email Body:</label>
					<div class="bg-slate-700 rounded-md p-4">
						<div class="text-white text-sm whitespace-pre-line">{isResendMode ? 'We are resending your verification details for the Cinemated project.' : 'Thanks a lot for your interest in the Cinemated project.'}

Before you can proceed to use the app, we need to verify your email. Please click on the following link to verify your email. If you did not request the access the app, you can safely ignore this email.

<span class="text-green-400 font-mono">{verificationData.verificationLink}</span>

After you successfully verify your email, you can use the following secret key to login. {isResendMode ? 'Please note that this is a NEW secret key - your previous one will no longer work.' : 'Please keep this secret key safe and secure.'} This is like your password and you will always need it to login to the Cinemated app.

<span class="text-yellow-400 font-mono">{verificationData.secretKey}</span>

We hope you will enjoy the Cinemated app. This app is still under development. We would like to hear your feedback about our app.

Thanks and best regards,
Moshfiqur</div>
					</div>
					<div class="mt-3 flex justify-end">
						<button
							on:click={() => copyToClipboard(`${isResendMode ? 'We are resending your verification details for the Cinemated project.' : 'Thanks a lot for your interest in the Cinemated project.'}

Before you can proceed to use the app, we need to verify your email. Please click on the following link to verify your email. If you did not request the access the app, you can safely ignore this email.

${verificationData.verificationLink}

After you successfully verify your email, you can use the following secret key to login. ${isResendMode ? 'Please note that this is a NEW secret key - your previous one will no longer work.' : 'Please keep this secret key safe and secure.'} This is like your password and you will always need it to login to the Cinemated app.

${verificationData.secretKey}

We hope you will enjoy the Cinemated app. This app is still under development. We would like to hear your feedback about our app.

Thanks and best regards,
Moshfiqur`, 'Email content copied!')}
							class="px-3 py-1 bg-turquoise-600 hover:bg-turquoise-700 text-white text-xs rounded"
						>
							Copy
						</button>
					</div>
				</div>
			</div>
		</div>

		<div class="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
			<div class="flex items-start">
				<svg class="w-5 h-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
				</svg>
				<div class="text-blue-300 text-sm">
					<p class="font-medium mb-1">Next Steps:</p>
					<ol class="list-decimal list-inside space-y-1 text-blue-300/80">
						<li>Copy the subject line and email content above</li>
						<li>Send the email to: <span class="text-turquoise-400 font-mono">{verificationData.email}</span></li>
						<li>User clicks the verification link to verify their email</li>
						<li>User can then login with their email and secret key</li>
					</ol>
				</div>
			</div>
		</div>
	</div>

	<div slot="footer" class="flex justify-between items-center">
		<div class="text-white/70 text-sm">
			Send to: <span class="text-turquoise-400 font-mono">{verificationData.email}</span>
		</div>
		<div class="flex space-x-3">
			<button
				on:click={() => showVerificationModal = false}
				class="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-md"
			>
				Close
			</button>
			<button
				on:click={() => copyToClipboard(`${isResendMode ? 'We are resending your verification details for the Cinemated project.' : 'Thanks a lot for your interest in the Cinemated project.'}

Before you can proceed to use the app, we need to verify your email. Please click on the following link to verify your email. If you did not request the access the app, you can safely ignore this email.

${verificationData.verificationLink}

After you successfully verify your email, you can use the following secret key to login. ${isResendMode ? 'Please note that this is a NEW secret key - your previous one will no longer work.' : 'Please keep this secret key safe and secure.'} This is like your password and you will always need it to login to the Cinemated app.

${verificationData.secretKey}

We hope you will enjoy the Cinemated app. This app is still under development. We would like to hear your feedback about our app.

Thanks and best regards,
Moshfiqur`, 'Complete email copied!')}
				class="px-4 py-2 bg-turquoise-600 hover:bg-turquoise-700 text-white rounded-md"
			>
				Copy Complete Email
			</button>
		</div>
	</div>
</Modal>
