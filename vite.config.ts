import { sveltekit } from '@sveltejs/kit/vite';
import type { UserConfig } from 'vite';

const config: UserConfig = {
	plugins: [sveltekit()],
	define: {
		global: 'globalThis'
	},
	ssr: {
		noExternal: []
	},
	optimizeDeps: {
		exclude: ['mongodb', 'bson', '@mongodb-js/saslprep', 'mongodb-connection-string-url']
	},
	build: {
		rollupOptions: {
			external: (id) => {
				// Exclude MongoDB and related packages from client bundle
				return id.includes('mongodb') ||
					   id.includes('bson') ||
					   id.includes('@mongodb-js') ||
					   id === 'stream' ||
					   id === 'net' ||
					   id === 'dns' ||
					   id === 'timers' ||
					   id === 'util' ||
					   id === 'zlib' ||
					   id === 'events' ||
					   id === 'fs' ||
					   id === 'crypto' ||
					   id === 'fs/promises' ||
					   id === 'tls' ||
					   id === 'http' ||
					   id === 'timers/promises' ||
					   id === 'child_process' ||
					   id === 'os' ||
					   id === 'process' ||
					   id === 'url';
			}
		}
	}
};

export default config;
