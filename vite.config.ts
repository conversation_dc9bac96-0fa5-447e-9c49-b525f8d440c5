import { sveltekit } from '@sveltejs/kit/vite';
import type { UserConfig } from 'vite';

const config: UserConfig = {
	plugins: [sveltekit()],
	define: {
		global: 'globalThis'
	},
	ssr: {
		noExternal: []
	},
	optimizeDeps: {
		exclude: ['mongodb', 'bson', '@mongodb-js/saslprep', 'mongodb-connection-string-url']
	},
	build: {
		rollupOptions: {
			external: (id) => {
				// Only exclude MongoDB and related packages from client bundle
				// Don't exclude SvelteKit or other framework modules
				if (id.startsWith('@sveltejs/') || id.startsWith('svelte/') || id.startsWith('$')) {
					return false;
				}

				return id.includes('mongodb') ||
					   id.includes('bson') ||
					   id.includes('@mongodb-js');
			}
		}
	}
};

export default config;
